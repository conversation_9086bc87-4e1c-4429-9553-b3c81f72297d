<template>
  <div class="payment-item">
    <div class="payment-item-date">
      <div>
        <div class="weekday d-none d-sm-block">
          {{ formatWeekday(item.date) }}
        </div>
        <div class="date d-none d-sm-block">
          {{ formatDate(item.date) }}
        </div>
        <div class="time d-none d-sm-block">{{ formatTime(item.time) }}</div>
        <!-- Mobile view -->
        <div class="d-sm-none">
          {{ formatWeekday(item.date) }}, {{ formatDate(item.date) }} -
          {{ formatTime(item.time) }}
        </div>
      </div>
      <div class="duration d-none d-sm-block">
        <div class="duration-icon">
          <svg width="18" height="18" viewBox="0 0 18 18">
            <use xlink:href="~/assets/images/icon-sprite.svg#clock-thin"></use>
          </svg>
          <span class="ml-1">{{
            $t('lessonLength_mins', { lessonLength: lessonLength })
          }}</span>
        </div>
      </div>
      <div class="duration d-sm-none">
        &nbsp;({{ $t('lessonLength_mins', { lessonLength: lessonLength }) }})
      </div>
    </div>

    <div class="payment-item-content">
      <div class="payment-info">
        <div class="student-name">{{ item.student }}</div>
        <div class="details">
          <div class="detail-group">
            <p class="label">Lesson:</p>
            <p class="value gradient-text">{{ item.lessonType }}</p>
          </div>
          <div class="detail-group">
            <p class="label">Finished:</p>
            <p class="value gradient-text">
              {{ formatFinishedAt(item.finishedAt) }}
            </p>
          </div>
          <div class="detail-group">
            <p class="label">Invoice no.</p>
            <p class="value gradient-text">{{ item.invoiceNo }}</p>
          </div>
          <div class="detail-group">
            <p class="label">Lesson no.</p>
            <p class="value gradient-text">{{ item.lessonNo }}</p>
          </div>
          <div class="detail-group">
            <p class="label">Value</p>
            <p class="value gradient-text">
              {{ formatCurrencyValue(item.value) }}
            </p>
          </div>
          <div
            v-if="item.transactionId && item.invoiceNumber"
            class="detail-group"
          >
            <p class="label">PDF</p>
            <p class="value">
              <a
                href="#"
                class="pdf-download-link gradient-text"
                @click.prevent="downloadPdf"
              >
                Download
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatCurrencyLocale } from '~/helpers'

const DEFAULT_PAYMENT_ITEM = {
  date: '2023-11-18',
  time: '9:00 AM',
  student: 'Kathrin Donaldson',
  lessonType: 'Trial',
  status: 'Finished',
  completedAt: '18 Nov, 10:02 AM',
  invoiceNo: '8395',
  lessonNo: '295032',
  value: '12.50',
  lessonLength: 30, // Default lesson length in minutes
}

export default {
  name: 'PaymentItem',
  props: {
    item: {
      type: Object,
      required: true,
      default: () => ({ ...DEFAULT_PAYMENT_ITEM }),
      validator(value) {
        return [
          'date',
          'time',
          'student',
          'lessonType',
          'status',
          'completedAt',
          'invoiceNo',
          'lessonNo',
          'value',
        ].every((key) => key in value)
      },
    },
  },
  computed: {
    lessonLength() {
      // If lessonLength is available in the item, use it, otherwise default to 30 minutes
      return this.item.lessonLength || 30
    },
    userLocale() {
      // Get user's UI language/locale, fallback to browser locale or 'en'
      return this.$store.getters['user/isUserLogged']
        ? this.$store.state.user.item?.uiLanguage || this.$i18n.locale
        : this.$i18n.locale || 'en'
    },
    timeZone() {
      // Get user's timezone, fallback to browser timezone
      return this.$store.getters['user/timeZone']
    },
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
    currentCurrency() {
      // Get current currency info from store
      return this.$store.state.currency.item
    },
  },
  methods: {
    formatDate(date) {
      try {
        return this.$dayjs(date).tz(this.timeZone).format('DD MMM')
      } catch (e) {
        // Fallback to default formatting if there's an error
        return date
      }
    },
    formatWeekday(date) {
      // Format weekday using user's locale and timezone
      try {
        // Use dayjs with timezone support and locale formatting
        return this.$dayjs(date).tz(this.timeZone).format('dddd')
      } catch (e) {
        // Fallback using Intl.DateTimeFormat with user's locale
        return new Intl.DateTimeFormat(this.userLocale, {
          weekday: 'long',
        }).format(new Date(date))
      }
    },
    formatTime(time) {
      // Format time using user's locale and timezone
      try {
        // If time is already in a good format, we can try to parse it with the date
        // and format it according to user's locale
        if (time && this.item.date) {
          // Combine date and time for proper timezone conversion
          const dateTimeString = `${this.item.date} ${time}`
          const dateTime = this.$dayjs(dateTimeString).tz(this.timeZone)

          // Format time using locale-aware format (LT = localized time)
          return dateTime.format('LT')
        }

        // Fallback: return the original time if we can't parse it
        return time
      } catch (e) {
        // Fallback to original time if there's an error
        return time
      }
    },
    formatFinishedAt(finishedAt) {
      // Format finished date/time using user's locale and timezone
      try {
        if (!finishedAt) return '-'

        // Use dayjs with timezone support and locale formatting
        // Format as "DD MMM, LT" (e.g., "18 Nov, 10:02 AM")
        return this.$dayjs(finishedAt).tz(this.timeZone).format('DD MMM, LT')
      } catch (e) {
        // Fallback to original value if there's an error
        return finishedAt || '-'
      }
    },
    formatValue(value) {
      // Format the value with exactly 2 decimal places
      return Number(value).toFixed(2)
    },
    formatCurrencyValue(value) {
      // Format currency value according to user's locale
      const currencyCode = this.currentCurrency?.isoCode || 'EUR'
      return formatCurrencyLocale(value, currencyCode, this.userLocale, true)
    },
    async downloadPdf() {
      try {
        await this.$store.dispatch('payments/downloadInvoicePdf', {
          transactionId: this.item.transactionId,
          invoiceNumber: this.item.invoiceNumber,
        })
      } catch (error) {
        // Handle error - show user-friendly message
        if (this.$store.dispatch) {
          this.$store.dispatch('snackbar/error', {
            errorMessage: 'Failed to download invoice PDF. Please try again.',
          })
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
$mobile-breakpoint: 768px;
$gradient-primary: linear-gradient(
  126.15deg,
  rgba(128, 182, 34, 0.74) 0%,
  rgba(60, 135, 248, 0.74) 102.93%
);
$gradient-text: linear-gradient(126.15deg, #80b622 0%, #3c87f8 102.93%);
$box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

.payment-item {
  display: flex;
  background: white;
  border-radius: 14px;
  margin-bottom: 12px;
  overflow: hidden;
  box-shadow: $box-shadow;

  &:hover {
    box-shadow: 0 4px 14px rgba(217, 225, 236, 0.47);
  }

  &-date {
    min-width: 100px;
    padding: 11px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 142px;
    border-radius: 16px;
    justify-content: center;
    background: $gradient-primary;
    color: white;
    box-shadow: 4px 0 8px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;

    .weekday {
      font-size: 13px;
      font-weight: 700;
      line-height: 1;
      text-transform: capitalize;
      text-align: center;
    }

    .date {
      font-size: 24px;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 2px;
    }

    .time {
      font-size: 13px;
      line-height: 1;
      font-weight: 700;
      margin-bottom: 18px;
      text-align: center;
    }
    .duration-icon {
      color: var(--v-dark-lighten3);
    }
    .duration {
      display: flex;
      align-items: center;
      font-size: 16px;
      span {
        color: #e8f1f7;
      }
      &-icon {
        margin-right: 4px;
        display: flex;
        align-items: center;
      }
    }
  }

  &-content {
    flex: 1;
    padding: 16px 24px;

    .payment-info {
      .student-name {
        font-size: 24px;
        font-weight: 500;
        color: #333;
        margin-bottom: 12px;
      }

      .details {
        display: flex;
        align-items: center;
        gap: 24px;
        font-size: 14px;

        .detail-group {
          align-items: center;
          gap: 6px;

          p {
            margin: 0;
          }

          .label {
            color: #666;
            font-size: 14px;
          }

          .value {
            color: #333;

            &.gradient-text {
              background: $gradient-text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              font-weight: 500;
              font-size: 16px;
              line-height: 18px;
            }
          }

          .pdf-download-link {
            cursor: pointer;
            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}

// Utility classes for responsive display
.d-none {
  display: none;
}

.d-sm-none {
  @media screen and (min-width: $mobile-breakpoint) {
    display: none;
  }
}

.d-sm-block {
  @media screen and (min-width: $mobile-breakpoint) {
    display: block;
  }
}

@media screen and (max-width: $mobile-breakpoint) {
  .payment-item {
    flex-direction: column;
    margin-bottom: 16px;
    box-shadow: none;
    box-shadow: 4px 0 8px rgba(0, 0, 0, 0.1);
    background: transparent;

    &-date {
      width: auto;
      min-height: auto;
      padding: 8px 16px;
      flex-direction: row;
      justify-content: flex-start;
      box-shadow: 4px 0 8px rgba(0, 0, 0, 0.1);
      border-radius: 24px;
      margin-bottom: 8px;

      .date {
        margin-right: 8px;
        margin-bottom: 0;
      }

      .time {
        margin-left: 0;
        opacity: 1;
        margin-bottom: 0;
      }
    }

    &-content {
      background: white;
      border-radius: 12px;
      padding: 16px;
      box-shadow: $box-shadow;

      .payment-info {
        .student-name {
          font-size: 20px; // Slightly smaller font size for mobile
          margin-bottom: 4px;
          padding-bottom: 12px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .details {
          flex-direction: column;
          gap: 8px;

          .detail-group {
            display: flex;
            justify-content: space-between;
            width: 100%;

            .value {
              font-size: 16px; // Adjusted for better mobile display
              font-weight: 500;
            }

            &:first-child {
              margin-bottom: 4px;
            }
          }
        }
      }
    }
  }
}
</style>
